import React, { useState, useEffect } from "react";
import { SelectionForm } from "./selection-form";
import { SelectionResult } from "./selection-result";
import { CalculatorLog } from "./calculator-log";
import {
  ElevatorSelectionData,
  CalculatedResult,
  getElevatorSelection,
  saveElevatorSelection,
  getDefaultSelectionData,
  Shaft,
} from "@/lib/elevator-selection";
import { OpportunityData } from "@/components/dashboard/opportunity-card";

interface SelectionContainerProps {
  opportunityData: OpportunityData;
  onSave: () => void;
  onDataReady?: () => void; // 新增：数据准备完成回调
}

export function SelectionContainer({ opportunityData, onSave, onDataReady }: SelectionContainerProps) {
  // 获取已保存的选型数据，现在数据已在页面级别预加载
  const [selectionData, setSelectionData] = useState<ElevatorSelectionData>(() => {
    const savedData = getElevatorSelection(opportunityData.id);
    if (savedData) {
      return savedData;
    }
    // 如果没有保存的数据，使用空的默认数据（这种情况理论上不应该发生，因为页面已预加载）
    return getDefaultSelectionData(opportunityData.id);
  });

  const [recommendWarnings, setRecommendWarnings] = useState<string[]>([]);
  const [recommendShaftData, setRecommendShaftData] = useState<Shaft | null>(null);
  const [showResult, setShowResult] = useState(false);

  // 计算结果
  const calculatedResult: CalculatedResult = {
    capacity: selectionData.capacity,
    persons: Math.floor(selectionData.capacity / 75),
    shaftWidth: selectionData.shaftWidth,
    shaftDepth: selectionData.shaftDepth,
    overheadHeight: selectionData.overhead,
    pitDepth: selectionData.pitDepth,
  };

  // 检查数据是否已准备好（包含推荐的井道参数）
  useEffect(() => {
    // 检查选型数据是否包含有效的井道参数（推荐值已加载）
    if (selectionData &&
        selectionData.shaftWidth > 0 &&
        selectionData.shaftDepth > 0 &&
        selectionData.overhead > 0 &&
        selectionData.pitDepth > 0 &&
        onDataReady) {
      // 数据已准备好，通知父组件
      onDataReady();
    }
  }, [selectionData, onDataReady]);

  // 更新选型数据
  const handleChange = (partialData: Partial<ElevatorSelectionData>) => {
    setSelectionData(prev => ({
      ...prev,
      ...partialData,
      lastUpdated: new Date().toISOString(),
    }));
  };

  // 保存选型数据
  const handleSave = () => {
    // 设置提交标志和时间
    const dataToSave = {
      ...selectionData,
      isSubmitted: true,
      submittedAt: new Date().toISOString(),
    };
    saveElevatorSelection(dataToSave);
    setSelectionData(dataToSave); // 更新本地状态
    onSave();
  };

  return (
    <div className="space-y-4 max-w-6xl mx-auto p-4 relative">
      {/* 顶部标题 - 公司标志和标题 */}
      <div className="flex items-center justify-center mb-4">
        <div className="text-center">
          <h1 className="text-xl font-bold mb-1">电梯土建尺寸规划</h1>
          <p className="text-md">Reassuring Planning Tool (RPT)</p>
        </div>
        <div className="w-32"></div> {/* 右侧占位 */}
      </div>

      {/* 项目信息展示 */}
      <div className="flex flex-col space-y-2 mb-4">
        <div className="flex items-center">
          <div className="w-28 font-semibold">项目名称:</div>
          <div>{opportunityData?.projectName || "N/A"}</div>
        </div>
        <div className="flex items-center">
          <div className="w-28 font-semibold">项目编号:</div>
          <div>{opportunityData?.projectCode || "N/A"}</div>
        </div>
        <div className="flex items-center">
          <div className="w-28 font-semibold">客户名称:</div>
          <div>{opportunityData?.customerName || "N/A"}</div>
        </div>
      </div>

      {/* 选型表单 */}
      <SelectionForm
        data={selectionData}
        setRecommendWarnings={setRecommendWarnings}
        recommendShaftData={recommendShaftData}
        setRecommendShaftData={setRecommendShaftData}
        onChange={handleChange}
        setShowResult={setShowResult}
      />

      {/* 选型结果 */}
        <SelectionResult
          result={calculatedResult}
          recommendShaftData={recommendShaftData}
          recommendWarnings={recommendWarnings}
        />


      {/* 计算器日志 - 浮动在右下角 */}
      <CalculatorLog data={selectionData} />
    </div>
  );
}
