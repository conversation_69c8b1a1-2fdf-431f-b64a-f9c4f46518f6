'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { SelectionContainer } from '@/components/elevator-selection';
import { hasElevatorSelection, getPresetDefaultSelectionData, saveElevatorSelection, ElevatorSelectionData } from '@/lib/elevator-selection';
import { OpportunityData } from '@/components/dashboard/opportunity-card';
import { ArrowLeft, Loader2 } from 'lucide-react';
import { calculateElevatorRecommend } from '@/api/calculate/api';
import { toast } from 'sonner';

// 从localStorage获取商机数据
const getOpportunityDataFromStorage = (id: string): OpportunityData | null => {
  if (typeof window === 'undefined') return null;
  
  try {
    const data = localStorage.getItem('opportunities');
    if (!data) return null;
    
    const opportunities = JSON.parse(data);
    return opportunities.find((opp: OpportunityData) => opp.id === id) || null;
  } catch (error) {
    console.error('Error getting opportunity data from localStorage:', error);
    return null;
  }
};

// 带步骤的加载状态组件
function LoadingStateWithStep({ step }: { step: string }) {
  return (
    <div className="flex items-center justify-center h-screen">
      <div className="text-center space-y-4">
        <Loader2 className="h-8 w-8 animate-spin mx-auto text-[#00B4AA]" />
        <div className="text-lg font-medium">正在初始化电梯选型</div>
        <div className="text-sm text-gray-600">{step}</div>
      </div>
    </div>
  );
}

// 错误状态组件
function ErrorState({ onBack }: { onBack: () => void }) {
  return (
    <div className="flex flex-col items-center justify-center h-screen">
      <div className="text-lg mb-4">无法加载商机数据</div>
      <Button onClick={onBack}>返回</Button>
    </div>
  );
}

// 电梯选型页面组件
export default function ElevatorSelectionPage() {
  const router = useRouter();
  const params = useParams();
  const id = params?.id as string;

  const [opportunityData, setOpportunityData] = useState<OpportunityData | null>(null);
  const [loading, setLoading] = useState(true);
  const [initializationStep, setInitializationStep] = useState<string>('加载商机数据...');
  const [isDataReady, setIsDataReady] = useState(false);

  // 初始化预设默认值和推荐参数
  const initializeDefaultData = async (opportunityId: string) => {
    try {
      // 检查是否已有选型数据，如果有则跳过初始化
      if (hasElevatorSelection(opportunityId)) {
        setInitializationStep('加载已有数据...');
        // 对于已有数据，让SelectionContainer组件来通知数据准备完成
        return;
      }

      setInitializationStep('设置默认参数...');

      // 获取预设默认值
      const defaultData = getPresetDefaultSelectionData(opportunityId);

      setInitializationStep('获取推荐井道参数...');

      // 调用推荐接口获取井道参数
      const baseData = {
        Lift_Model: defaultData.liftModel,
        Capacity: defaultData.capacity,
        Speed: defaultData.speed,
        Travel_Height: defaultData.travelHeight,
        Car_Width: defaultData.carWidth,
        Car_Depth: defaultData.carDepth,
        Car_Height: defaultData.carHeight,
        CWT_Position: defaultData.cwtPosition,
        CWT_Safety_Gear: defaultData.cwtSafetyGear ? "Yes" : "No",
        Door_Opening: defaultData.doorOpening,
        Door_Width: defaultData.doorWidth,
        Door_Height: defaultData.doorHeight,
        Through_Door: defaultData.throughDoor ? "Yes" : "No",
        Glass_Door: defaultData.glassDoor ? "Yes" : "No",
        Standard: defaultData.standard,
        Door_Center_from_Car_Center: defaultData.doorCenterPosition,
        Car_Area_Exceed_the_Code: defaultData.floorExceedCode ? "Yes" : "No Allow",
        Shaft_Tolerance: defaultData.shaftTolerance,
        Marble_Floor: defaultData.marbleFloorThickness,
        Shaft_Width: 0,
        Shaft_Depth: 0,
        Overhead: 0,
        Pit_Depth: 0,
      };

      const { data: recommend } = await calculateElevatorRecommend(baseData);

      // 更新默认数据，包含推荐的井道参数
      const completeDefaultData: ElevatorSelectionData = {
        ...defaultData,
        shaftWidth: recommend.Shaft_Width_min,
        shaftDepth: recommend.Shaft_Depth_min,
        overhead: recommend.Shaft_Height_min,
        pitDepth: recommend.Shaft_Pit_min,
      };

      setInitializationStep('保存初始数据...');

      // 保存到localStorage
      saveElevatorSelection(completeDefaultData);

      setInitializationStep('数据准备完成');

      // 标记数据已准备好
      setIsDataReady(true);

    } catch (error) {
      console.error("初始化默认数据失败", error);
      toast.error("初始化默认数据失败，请稍后重试");
      setInitializationStep('初始化失败');
      setIsDataReady(false);
    }
  };

  useEffect(() => {
    const initializePage = async () => {
      if (id) {
        setLoading(true);

        try {
          // 获取商机数据
          setInitializationStep('加载商机数据...');
          const data = getOpportunityDataFromStorage(id);
          setOpportunityData(data);

          if (data) {
            // 初始化默认数据和推荐参数
            await initializeDefaultData(id);
          } else {
            setIsDataReady(true); // 如果没有商机数据，也标记为准备完成以显示错误状态
          }
        } catch (error) {
          console.error("页面初始化失败", error);
          toast.error("页面初始化失败");
          setIsDataReady(true); // 即使出错也要移除loading
        }
      }
    };

    initializePage();
  }, [id]);

  // 监听数据准备状态，只有在数据完全准备好后才移除loading
  useEffect(() => {
    if (isDataReady) {
      // 添加一个小延迟，确保UI完全渲染
      const timer = setTimeout(() => {
        setLoading(false);
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [isDataReady]);

  const handleSave = () => {
    // 保存成功后返回商机详情页
    router.push(`/dashboard/opportunity/${id}`);
  };

  const handleBack = () => {
    router.back();
  };

  if (loading) {
    return <LoadingStateWithStep step={initializationStep} />;
  }

  if (!opportunityData) {
    return <ErrorState onBack={handleBack} />;
  }

  return (
    <div className="container mx-auto py-6 px-4">
      <div className="flex items-center mb-6">
        <Button variant="ghost" className="mr-4" onClick={handleBack}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          返回
        </Button>
        <h1 className="text-2xl font-bold">电梯选型 - {opportunityData.projectName}</h1>
      </div>

      <SelectionContainer
        opportunityData={opportunityData}
        onSave={handleSave}
        onDataReady={() => setIsDataReady(true)}
      />
    </div>
  );
}